module go-abm-idp-v2

go 1.23.0

require (
    github.com/casbin/casbin/v2 v2.104.0
    github.com/casbin/gorm-adapter/v3 v3.25.0
    github.com/fsnotify/fsnotify v1.8.0
    github.com/gin-gonic/gin v1.10.0
    github.com/go-playground/validator/v10 v10.23.0
    github.com/go-redis/redis/v8 v8.11.5
    github.com/golang-jwt/jwt/v5 v5.2.2
    github.com/google/uuid v1.6.0
    github.com/google/wire v0.6.0
    github.com/prometheus/client_golang v1.20.5
    github.com/rogpeppe/go-internal v1.14.0
    github.com/sagikazarmark/locafero v0.7.0
    github.com/sourcegraph/conc v0.3.0
    github.com/spf13/afero v1.12.0
    github.com/spf13/cast v1.7.1
    github.com/spf13/pflag v1.0.6
    github.com/spf13/viper v1.20.0
    github.com/stretchr/testify v1.9.0
    github.com/subosito/gotenv v1.6.0
    go.uber.org/zap v1.27.0
    golang.org/x/crypto v0.36.0
    golang.org/x/exp v0.0.0-20221208152030-732eee02a75a
    gopkg.in/check.v1 v1.0.0-20201130134442-10cb98267c6c
    gopkg.in/natefinch/lumberjack.v2 v2.2.1
    gorm.io/driver/mysql v1.5.7
    gorm.io/driver/postgres v1.5.11
    gorm.io/driver/sqlserver v1.5.3
    gorm.io/gorm v1.30.0
)

require (
    github.com/beorn7/perks v1.0.1 // indirect
    github.com/bytedance/sonic v1.12.6 // indirect
    github.com/bytedance/sonic/loader v0.2.1 // indirect
    github.com/casbin/govaluate v1.3.0 // indirect
    github.com/cespare/xxhash/v2 v2.3.0 // indirect
    github.com/cloudwego/base64x v0.1.4 // indirect
    github.com/cloudwego/iasm v0.2.0 // indirect
    github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
    github.com/gabriel-vasile/mimetype v1.4.7 // indirect
    github.com/gin-contrib/sse v0.1.0 // indirect
    github.com/go-playground/locales v0.14.1 // indirect
    github.com/go-playground/universal-translator v0.18.1 // indirect
    github.com/go-sql-driver/mysql v1.7.0 // indirect
    github.com/goccy/go-json v0.10.4 // indirect
    github.com/golang-sql/civil v0.0.0-20220223132316-b832511892a9 // indirect
    github.com/golang-sql/sqlexp v0.1.0 // indirect
    github.com/jackc/pgpassfile v1.0.0 // indirect
    github.com/jackc/pgservicefile v0.0.0-20221227161230-091c0ba34f0a // indirect
    github.com/jackc/pgx/v5 v5.5.5 // indirect
    github.com/jackc/puddle/v2 v2.2.1 // indirect
    github.com/jinzhu/inflection v1.0.0 // indirect
    github.com/jinzhu/now v1.1.5 // indirect
    github.com/json-iterator/go v1.1.12 // indirect
    github.com/klauspost/cpuid/v2 v2.2.9 // indirect
    github.com/kr/pretty v0.3.1 // indirect
    github.com/leodido/go-urn v1.4.0 // indirect
    github.com/mattn/go-isatty v0.0.20 // indirect
    github.com/microsoft/go-mssqldb v1.6.0 // indirect
    github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
    github.com/modern-go/reflect2 v1.0.2 // indirect
    github.com/pelletier/go-toml/v2 v2.2.3 // indirect
    github.com/prometheus/client_model v0.6.1 // indirect
    github.com/prometheus/common v0.55.0 // indirect
    github.com/prometheus/procfs v0.15.1 // indirect
    github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
    github.com/ugorji/go/codec v1.2.12 // indirect
    go.uber.org/multierr v1.10.0 // indirect
    golang.org/x/arch v0.12.0 // indirect
    golang.org/x/net v0.34.0 // indirect
    golang.org/x/sync v0.12.0 // indirect
    golang.org/x/sys v0.31.0 // indirect
    golang.org/x/text v0.23.0 // indirect
    google.golang.org/protobuf v1.36.4 // indirect
    gopkg.in/yaml.v3 v3.0.1 // indirect
)