package app

import (
	"context"
	"net/http"
	"sync"

	"go-abm-idp-v2/internal/infrastructure/config"
	"go-abm-idp-v2/internal/interfaces"
	"go-abm-idp-v2/pkg/logger"
)

type Application struct {
	config          *config.Config
	logger          logger.Logger
	httpServer      *interfaces.HTTPServer
	shutdownServers []ShutdownServer
}

type ShutdownServer interface {
	Shutdown(ctx context.Context) error
}

func NewApplication(ctx context.Context) (*Application, error) {
	// 1. 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		return nil, err
	}

	// 2. 初始化日志
	log := logger.NewLogger(cfg.LogLevel)

	// 3. 创建HTTP服务器
	httpServer := interfaces.NewHTTPServer(cfg, log)

	return &Application{
		config:     cfg,
		logger:     log,
		httpServer: httpServer,
	}, nil
}

func (a *Application) Start() error {
	a.logger.Info("Starting IAM application server...")

	// 启动HTTP服务器
	go func() {
		if err := a.httpServer.Start(); err != nil && err != http.ErrServerClosed {
			a.logger.Fatal("HTTP server error", logger.Error(err))
		}
	}()

	a.logger.Info("IAM application started successfully",
		logger.String("address", a.config.Server.Address))

	return nil
}

func (a *Application) Stop(ctx context.Context) error {
	a.logger.Info("Shutting down application...")

	// 使用WaitGroup等待所有服务关闭
	var wg sync.WaitGroup
	errChan := make(chan error, len(a.shutdownServers)+1)

	// 关闭HTTP服务器
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := a.httpServer.Shutdown(ctx); err != nil {
			errChan <- err
		}
	}()

	// 关闭其他服务
	for _, srv := range a.shutdownServers {
		wg.Add(1)
		go func(s ShutdownServer) {
			defer wg.Done()
			if err := s.Shutdown(ctx); err != nil {
				errChan <- err
			}
		}(srv)
	}

	// 等待所有关闭操作完成
	wg.Wait()
	close(errChan)

	// 检查是否有错误
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	a.logger.Info("Application shutdown complete")
	return nil
}

// RegisterShutdownServer 注册需要优雅关闭的服务
func (a *Application) RegisterShutdownServer(server ShutdownServer) {
	a.shutdownServers = append(a.shutdownServers, server)
}
